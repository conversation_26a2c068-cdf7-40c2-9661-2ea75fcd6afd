#!/usr/bin/env python3
"""
Detailed test script for LUQ implementation with various scenarios
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_luq_consistent_responses():
    """Test LUQ with highly consistent responses (should have low uncertainty)"""
    try:
        from uq_methods.implementations.luq import LUQUQ
        
        luq = LUQUQ(method="binary", verbose=True)
        
        # Very similar responses
        responses = [
            "<PERSON> was born on March 31, 1942. He is an American radio host.",
            "<PERSON> was born March 31, 1942. He hosts American radio shows.",
            "<PERSON> was born on March 31, 1942. He is a radio host in America."
        ]
        
        result = luq.compute_uncertainty(responses)
        uncertainty = result["uncertainty_score"]
        
        print(f"✓ Consistent responses test passed")
        print(f"  Uncertainty score: {uncertainty:.4f} (should be relatively low)")
        
        return True
    except Exception as e:
        print(f"✗ Consistent responses test failed: {e}")
        return False

def test_luq_inconsistent_responses():
    """Test LUQ with inconsistent responses (should have high uncertainty)"""
    try:
        from uq_methods.implementations.luq import LUQUQ
        
        luq = LUQUQ(method="binary", verbose=True)
        
        # Contradictory responses
        responses = [
            "<PERSON> <PERSON>ner was born on March 31, 1942. He is an American radio host.",
            "<PERSON> <PERSON> <PERSON>ner was born on January 13, 1960. He is a Canadian journalist.",
            "<PERSON> <PERSON> <PERSON>ner was born on July 4, 1975. He is a British television producer."
        ]
        
        result = luq.compute_uncertainty(responses)
        uncertainty = result["uncertainty_score"]
        
        print(f"✓ Inconsistent responses test passed")
        print(f"  Uncertainty score: {uncertainty:.4f} (should be relatively high)")
        
        return True
    except Exception as e:
        print(f"✗ Inconsistent responses test failed: {e}")
        return False

def test_luq_multiclass_method():
    """Test LUQ with multiclass method"""
    try:
        from uq_methods.implementations.luq import LUQUQ
        
        luq = LUQUQ(method="multiclass", verbose=True)
        
        responses = [
            "The capital of France is Paris. It is located in Europe.",
            "The capital of France is London. It is in the United Kingdom.",
            "France is a country in Europe. Paris is its capital city."
        ]
        
        result = luq.compute_uncertainty(responses)
        uncertainty = result["uncertainty_score"]
        
        print(f"✓ Multiclass method test passed")
        print(f"  Uncertainty score: {uncertainty:.4f}")
        print(f"  Method type: {result['method_type']}")
        
        return True
    except Exception as e:
        print(f"✗ Multiclass method test failed: {e}")
        return False

def test_luq_abridged_mode():
    """Test LUQ with abridged mode (first sentence only)"""
    try:
        from uq_methods.implementations.luq import LUQUQ
        
        luq = LUQUQ(method="binary", abridged=True, verbose=True)
        
        responses = [
            "The Earth is round. It orbits around the Sun. Water covers most of its surface.",
            "The Earth is flat. It is the center of the universe. Land covers most of its surface.",
            "The Earth is spherical. It revolves around the Sun. Oceans cover most of its surface."
        ]
        
        result = luq.compute_uncertainty(responses)
        uncertainty = result["uncertainty_score"]
        
        print(f"✓ Abridged mode test passed")
        print(f"  Uncertainty score: {uncertainty:.4f}")
        print(f"  Abridged: {result['abridged']}")
        
        return True
    except Exception as e:
        print(f"✗ Abridged mode test failed: {e}")
        return False

def test_luq_edge_cases():
    """Test LUQ with edge cases"""
    try:
        from uq_methods.implementations.luq import LUQUQ
        
        luq = LUQUQ(method="binary", verbose=False)
        
        # Test with single response (should return error)
        result1 = luq.compute_uncertainty(["Single response"])
        assert "error" in result1
        print("✓ Single response handled correctly")
        
        # Test with empty sentences
        result2 = luq.compute_uncertainty(["", ""])
        print(f"✓ Empty responses handled: uncertainty = {result2['uncertainty_score']:.4f}")
        
        # Test with very short responses
        result3 = luq.compute_uncertainty(["Yes", "No", "Maybe"])
        print(f"✓ Short responses handled: uncertainty = {result3['uncertainty_score']:.4f}")
        
        return True
    except Exception as e:
        print(f"✗ Edge cases test failed: {e}")
        return False

def test_luq_method_comparison():
    """Compare binary vs multiclass methods"""
    try:
        from uq_methods.implementations.luq import LUQUQ
        
        responses = [
            "Python is a programming language. It was created by Guido van Rossum.",
            "Python is a snake species. It is found in tropical regions.",
            "Python is a programming language. It is popular for data science."
        ]
        
        # Binary method
        luq_binary = LUQUQ(method="binary", verbose=False)
        result_binary = luq_binary.compute_uncertainty(responses)
        
        # Multiclass method
        luq_multiclass = LUQUQ(method="multiclass", verbose=False)
        result_multiclass = luq_multiclass.compute_uncertainty(responses)
        
        print(f"✓ Method comparison test passed")
        print(f"  Binary uncertainty: {result_binary['uncertainty_score']:.4f}")
        print(f"  Multiclass uncertainty: {result_multiclass['uncertainty_score']:.4f}")
        
        return True
    except Exception as e:
        print(f"✗ Method comparison test failed: {e}")
        return False

def main():
    """Run all detailed tests"""
    print("Detailed LUQ Testing")
    print("=" * 60)
    
    tests = [
        test_luq_consistent_responses,
        test_luq_inconsistent_responses,
        test_luq_multiclass_method,
        test_luq_abridged_mode,
        test_luq_edge_cases,
        test_luq_method_comparison,
    ]
    
    results = []
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        results.append(test())
    
    print("\n" + "=" * 60)
    print(f"Detailed Test Results: {sum(results)}/{len(results)} passed")
    
    if sum(results) == len(results):
        print("🎉 All detailed tests passed! LUQ implementation is working correctly.")
    else:
        print("⚠️  Some detailed tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
