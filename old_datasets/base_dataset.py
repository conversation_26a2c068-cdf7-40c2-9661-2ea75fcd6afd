"""
Base dataset class for uncertainty analysis datasets.

This module provides abstract base classes and utilities for managing datasets
used in uncertainty quantification tasks.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any
import os
import random


class BaseDataset(ABC):
    """Abstract base class for all datasets used in uncertainty analysis."""
    
    def __init__(self, name: str, split: str = "test", data_dir: str = None):
        self.name = name
        self.split = split
        self.data_dir = data_dir or os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            "data", 
            name
        )
        self.data = []
        self.load_data()
    
    @abstractmethod
    def load_data(self) -> List[Dict[str, Any]]:
        """Load dataset from storage."""
        pass
    
    def sample(self, n: int, method: str = "random", seed: int = None) -> List[Dict[str, Any]]:
        """Sample n questions from the dataset.
        
        Args:
            n: Number of samples
            method: Sampling method ("random", "stratified", "sequential")
            seed: Random seed for reproducibility
        """
        if seed is not None:
            random.seed(seed)
            
        if n > len(self.data):
            n = len(self.data)
            
        if method == "random":
            return random.sample(self.data, n)
        elif method == "stratified":
            return self._stratified_sample(n)
        elif method == "sequential":
            return self.data[:n]
        else:
            raise ValueError(f"Unknown sampling method: {method}")
    
    def _stratified_sample(self, n: int) -> List[Dict[str, Any]]:
        """Stratified sampling by category."""
        categories = {}
        for item in self.data:
            cat = item.get('category', 'unknown')
            categories.setdefault(cat, []).append(item)
        
        samples = []
        items_per_category = max(1, n // len(categories))
        
        for cat, items in categories.items():
            cat_samples = min(items_per_category, len(items))
            samples.extend(random.sample(items, cat_samples))
        
        # Fill remaining if needed
        remaining = n - len(samples)
        if remaining > 0:
            remaining_items = [item for item in self.data if item not in samples]
            samples.extend(random.sample(remaining_items, min(remaining, len(remaining_items))))
        
        return samples
    
    def filter_by_category(self, categories: List[str]) -> List[Dict[str, Any]]:
        """Filter questions by category."""
        if not categories:
            return self.data
        return [item for item in self.data if item.get('category', '').lower() in [cat.lower() for cat in categories]]
    
    def get_categories(self) -> List[str]:
        """Get list of unique categories in the dataset."""
        return list(set(item.get('category', 'unknown') for item in self.data))
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get dataset metadata."""
        return {
            "name": self.name,
            "split": self.split,
            "size": len(self.data),
            "categories": self.get_categories()
        }