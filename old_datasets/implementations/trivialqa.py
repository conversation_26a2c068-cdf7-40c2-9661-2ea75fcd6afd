"""
TrivialQA dataset implementation - Simple Q&A dataset for testing
"""
from typing import List, Dict, Any
import random
from datasets.base import Dataset


class TrivialQADataset(Dataset):
    """Simple Q&A dataset for testing purposes"""
    
    def __init__(self, name: str, sample_size: int = 100, random_seed: int = 42):
        super().__init__(name, sample_size, random_seed)
        self.description = "Simple Q&A dataset with basic questions for testing"
        self.questions = [
            {
                "question": "What is the capital of France?",
                "answer": "Paris",
                "context": "Geography question",
                "category": "geography"
            },
            {
                "question": "What is 2 + 2?",
                "answer": "4",
                "context": "Basic arithmetic",
                "category": "math"
            },
            {
                "question": "What color is the sky?",
                "answer": "blue",
                "context": "General knowledge",
                "category": "general"
            },
            {
                "question": "How many days are in a week?",
                "answer": "7",
                "context": "Time measurement",
                "category": "time"
            },
            {
                "question": "What is the largest planet in our solar system?",
                "answer": "Jupiter",
                "context": "Astronomy",
                "category": "science"
            },
            {
                "question": "Who wrote '<PERSON> and Juliet'?",
                "answer": "William Shakespeare",
                "context": "Literature",
                "category": "literature"
            },
            {
                "question": "What is the freezing point of water in Celsius?",
                "answer": "0",
                "context": "Physics",
                "category": "science"
            },
            {
                "question": "How many continents are there?",
                "answer": "7",
                "context": "Geography",
                "category": "geography"
            },
            {
                "question": "What is the currency of Japan?",
                "answer": "Japanese Yen",
                "context": "Economics",
                "category": "economics"
            },
            {
                "question": "Who painted the Mona Lisa?",
                "answer": "Leonardo da Vinci",
                "context": "Art history",
                "category": "art"
            }
        ]
    
    async def load_samples(self) -> List[Dict[str, Any]]:
        """
        Load samples from the TrivialQA dataset
        
        Returns:
            List of sample dictionaries with Q&A pairs
        """
        # Set random seed for reproducibility
        random.seed(self.random_seed)
        
        # Create samples by cycling through questions
        samples = []
        for i in range(self.sample_size):
            base_question = self.questions[i % len(self.questions)]
            
            # Create variations
            sample = {
                'question': base_question['question'],
                'answer': base_question['answer'],
                'context': base_question['context'],
                'metadata': {
                    'id': i,
                    'category': base_question['category'],
                    'difficulty': random.choice(['easy', 'medium', 'hard']),
                    'variation': i // len(self.questions) + 1
                }
            }
            samples.append(sample)
        
        # Shuffle to mix categories
        random.shuffle(samples)
        
        return samples[:self.sample_size]
    
    def get_info(self) -> Dict[str, Any]:
        """Get dataset information"""
        return {
            'name': self.name,
            'description': self.description,
            'sample_size': self.sample_size,
            'random_seed': self.random_seed,
            'question_count': len(self.questions),
            'categories': list(set(q['category'] for q in self.questions))
        }