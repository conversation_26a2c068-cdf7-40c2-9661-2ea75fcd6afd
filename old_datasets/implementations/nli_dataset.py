"""
NLI dataset implementation for Natural Language Inference tasks
"""
from typing import List, Dict, Any
import random
from datasets.base import Dataset


class NLIDataset(Dataset):
    """Natural Language Inference dataset for uncertainty testing"""
    
    def __init__(self, name: str, sample_size: int = 100, random_seed: int = 42):
        super().__init__(name, sample_size, random_seed)
        self.description = "NLI dataset with premise-hypothesis pairs for uncertainty evaluation"
        
        # Sample NLI pairs with different relationships
        self.nli_samples = [
            {
                "premise": "A man is playing guitar on stage.",
                "hypothesis": "A person is performing music.",
                "relationship": "entailment",
                "category": "entertainment"
            },
            {
                "premise": "The cat is sleeping on the couch.",
                "hypothesis": "The cat is running outside.",
                "relationship": "contradiction",
                "category": "animals"
            },
            {
                "premise": "The meeting started at 3 PM.",
                "hypothesis": "The weather is sunny.",
                "relationship": "neutral",
                "category": "business"
            },
            {
                "premise": "The students are studying in the library.",
                "hypothesis": "Students are learning.",
                "relationship": "entailment",
                "category": "education"
            },
            {
                "premise": "The car is red.",
                "hypothesis": "The car is blue.",
                "relationship": "contradiction",
                "category": "objects"
            },
            {
                "premise": "She bought a new phone yesterday.",
                "hypothesis": "She has a smartphone.",
                "relationship": "neutral",
                "category": "technology"
            },
            {
                "premise": "The restaurant was busy last night.",
                "hypothesis": "Many people were eating dinner.",
                "relationship": "entailment",
                "category": "food"
            },
            {
                "premise": "The book is on the table.",
                "hypothesis": "The book is in the drawer.",
                "relationship": "contradiction",
                "category": "objects"
            }
        ]
    
    async def load_samples(self) -> List[Dict[str, Any]]:
        """
        Load NLI samples for uncertainty evaluation
        
        Returns:
            List of NLI sample dictionaries
        """
        random.seed(self.random_seed)
        
        samples = []
        for i in range(self.sample_size):
            base_sample = self.nli_samples[i % len(self.nli_samples)]
            
            # Create variations by adding slight perturbations
            sample = {
                'premise': base_sample['premise'],
                'hypothesis': base_sample['hypothesis'],
                'relationship': base_sample['relationship'],
                'context': f"NLI task - {base_sample['category']}",
                'metadata': {
                    'id': i,
                    'category': base_sample['category'],
                    'expected_label': base_sample['relationship'],
                    'variation': i // len(self.nli_samples) + 1,
                    'difficulty': random.choice(['easy', 'medium', 'hard'])
                }
            }
            samples.append(sample)
        
        # Shuffle to mix relationships
        random.shuffle(samples)
        
        return samples[:self.sample_size]
    
    def get_info(self) -> Dict[str, Any]:
        """Get dataset information"""
        relationship_counts = {}
        for sample in self.nli_samples:
            rel = sample['relationship']
            relationship_counts[rel] = relationship_counts.get(rel, 0) + 1
        
        return {
            'name': self.name,
            'description': self.description,
            'sample_size': self.sample_size,
            'random_seed': self.random_seed,
            'relationship_counts': relationship_counts,
            'categories': list(set(s['category'] for s in self.nli_samples))
        }