"""
Comprehensive result management for uncertainty analysis tasks.

This module provides utilities for saving and managing uncertainty analysis results
with full task configuration tracking.
"""

import csv
import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import hashlib


class TaskConfiguration:
    """Configuration object for uncertainty analysis tasks."""
    
    def __init__(self, 
                 dataset_name: str,
                 evaluation_method: str,
                 dataset_config: Dict[str, Any],
                 evaluation_config: Dict[str, Any],
                 environment_config: Dict[str, Any]):
        self.task_id = self._generate_task_id(dataset_name, evaluation_method)
        self.timestamp = datetime.now().isoformat()
        self.dataset_name = dataset_name
        self.evaluation_method = evaluation_method
        self.dataset_config = dataset_config
        self.evaluation_config = evaluation_config
        self.environment_config = environment_config
    
    def _generate_task_id(self, dataset_name: str, evaluation_method: str) -> str:
        """Generate unique task ID based on configuration."""
        config_str = f"{dataset_name}_{evaluation_method}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        task_hash = hashlib.md5(config_str.encode()).hexdigest()[:8]
        return f"{dataset_name}_{evaluation_method}_{task_hash}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "task_id": self.task_id,
            "timestamp": self.timestamp,
            "description": f"{self.evaluation_method} uncertainty evaluation on {self.dataset_name}",
            "dataset": self.dataset_config,
            "evaluation": self.evaluation_config,
            "environment": self.environment_config
        }


class UncertaintyResultsSaver:
    """Comprehensive result saving for uncertainty analysis."""
    
    def __init__(self, base_dir: str = "./results"):
        self.base_dir = base_dir
        self.ensure_directories()
    
    def ensure_directories(self):
        """Ensure all required directories exist."""
        directories = [
            "by_task",
            "comparisons",
            "summaries",
            "raw_outputs",
            "uncertainty_scores"
        ]
        for directory in directories:
            os.makedirs(os.path.join(self.base_dir, directory), exist_ok=True)
    
    def save_task_config(self, task_config: TaskConfiguration):
        """Save task configuration."""
        config_path = os.path.join(
            self.base_dir, 
            "by_task", 
            task_config.task_id, 
            "task_config.json"
        )
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(task_config.to_dict(), f, indent=2, ensure_ascii=False)
    
    def save_individual_results(self, 
                              task_config: TaskConfiguration, 
                              results: List[Dict[str, Any]]):
        """Save individual question results as CSV."""
        csv_path = os.path.join(
            self.base_dir,
            "by_task",
            task_config.task_id,
            "individual_results.csv"
        )
        os.makedirs(os.path.dirname(csv_path), exist_ok=True)
        
        if not results:
            return
        
        fieldnames = [
            "task_id",
            "question_id",
            "question_text",
            "expected_answer",
            "category",
            "semantic_entropy",
            "num_clusters",
            "uncertainty_level",
            "max_entropy_possible",
            "entropy_normalized",
            "avg_response_length",
            "num_responses",
            "timestamp"
        ]
        
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in results:
                row = {
                    "task_id": task_config.task_id,
                    "question_id": result.get("question_id", "unknown"),
                    "question_text": result.get("question", ""),
                    "expected_answer": result.get("expected_answer", ""),
                    "category": result.get("category", "unknown"),
                    "semantic_entropy": result.get("semantic_entropy", 0.0),
                    "num_clusters": result.get("num_clusters", 0),
                    "uncertainty_level": result.get("uncertainty_level", "unknown"),
                    "max_entropy_possible": result.get("max_entropy_possible", 0.0),
                    "entropy_normalized": result.get("entropy_normalized", 0.0),
                    "avg_response_length": result.get("avg_response_length", 0.0),
                    "num_responses": result.get("num_responses", 0),
                    "timestamp": task_config.timestamp
                }
                writer.writerow(row)
    
    def save_aggregate_results(self, 
                             task_config: TaskConfiguration, 
                             results: List[Dict[str, Any]]):
        """Save aggregate statistics as CSV."""
        csv_path = os.path.join(
            self.base_dir,
            "by_task",
            task_config.task_id,
            "aggregate_results.csv"
        )
        os.makedirs(os.path.dirname(csv_path), exist_ok=True)
        
        if not results:
            return
        
        entropies = [r.get("semantic_entropy", 0.0) for r in results]
        
        aggregate = {
            "task_id": task_config.task_id,
            "dataset_name": task_config.dataset_name,
            "evaluation_method": task_config.evaluation_method,
            "sample_size": len(results),
            "avg_entropy": sum(entropies) / len(entropies) if entropies else 0.0,
            "std_entropy": self._calculate_std(entropies),
            "min_entropy": min(entropies) if entropies else 0.0,
            "max_entropy": max(entropies) if entropies else 0.0,
            "median_entropy": self._calculate_median(entropies),
            "high_uncertainty_count": sum(1 for e in entropies if e > 2.0),
            "medium_uncertainty_count": sum(1 for e in entropies if 1.0 <= e <= 2.0),
            "low_uncertainty_count": sum(1 for e in entropies if e < 1.0),
            "total_questions": len(results),
            "timestamp": task_config.timestamp
        }
        
        fieldnames = list(aggregate.keys())
        
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerow(aggregate)
    
    def save_raw_outputs(self, 
                        task_config: TaskConfiguration, 
                        results: List[Dict[str, Any]]):
        """Save raw model outputs as JSONL."""
        jsonl_path = os.path.join(
            self.base_dir,
            "by_task",
            task_config.task_id,
            "raw_outputs.jsonl"
        )
        os.makedirs(os.path.dirname(jsonl_path), exist_ok=True)
        
        with open(jsonl_path, 'w', encoding='utf-8') as f:
            for result in results:
                output = {
                    "task_id": task_config.task_id,
                    "question_id": result.get("question_id", "unknown"),
                    "question": result.get("question", ""),
                    "expected_answer": result.get("expected_answer", ""),
                    "responses": result.get("responses", []),
                    "clusters": result.get("clusters", []),
                    "analysis": {
                        "semantic_entropy": result.get("semantic_entropy", 0.0),
                        "num_clusters": result.get("num_clusters", 0),
                        "uncertainty_level": result.get("uncertainty_level", "unknown")
                    },
                    "metadata": {
                        "category": result.get("category", "unknown"),
                        "timestamp": task_config.timestamp
                    }
                }
                f.write(json.dumps(output, ensure_ascii=False) + '\n')
    
    def save_all_results(self, 
                        task_config: TaskConfiguration, 
                        results: List[Dict[str, Any]]):
        """Save all result formats."""
        self.save_task_config(task_config)
        self.save_individual_results(task_config, results)
        self.save_aggregate_results(task_config, results)
        self.save_raw_outputs(task_config, results)
    
    def _calculate_std(self, values: List[float]) -> float:
        """Calculate standard deviation."""
        if len(values) <= 1:
            return 0.0
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance ** 0.5
    
    def _calculate_median(self, values: List[float]) -> float:
        """Calculate median."""
        if not values:
            return 0.0
        sorted_values = sorted(values)
        n = len(sorted_values)
        if n % 2 == 0:
            return (sorted_values[n//2 - 1] + sorted_values[n//2]) / 2
        return sorted_values[n//2]
    
    def get_task_directory(self, task_config: TaskConfiguration) -> str:
        """Get task-specific directory path."""
        return os.path.join(self.base_dir, "by_task", task_config.task_id)
    
    def list_tasks(self) -> List[str]:
        """List all task IDs."""
        tasks_dir = os.path.join(self.base_dir, "by_task")
        if not os.path.exists(tasks_dir):
            return []
        return [d for d in os.listdir(tasks_dir) 
                if os.path.isdir(os.path.join(tasks_dir, d))]