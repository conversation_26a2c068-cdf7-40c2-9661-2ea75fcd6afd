#!/usr/bin/env python3
"""
Test LUQ implementation using the same data as test_ecc_methods.py
"""

import json
import sys
import os
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from pymongo import MongoClient
from tqdm import tqdm

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import LUQ implementation
from uq_methods.implementations.luq import LUQUQ


def get_sample_group(col, task_name: str, dataset_source: str, limit_responses: int = 40) -> Optional[Dict[str, Any]]:
    """
    Get a sample group with specified number of responses for a given task.
    Groups by prompt_seed - each seed should have exactly 40 responses for the same input.
    """
    # Find a prompt_seed with exactly the required number of responses
    pipeline = [
        {
            "$match": {
                "task_name": task_name,
                "dataset_source": dataset_source
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source",
                    "prompt_seed": "$prompt_seed",
                },
                "count": {"$sum": 1},
                "docs": {"$push": "$$ROOT"}
            }
        },
        {
            "$match": {
                "count": {"$eq": limit_responses}  # Exactly 40 responses
            }
        },
        {
            "$limit": 1
        }
    ]

    result = list(col.aggregate(pipeline))
    if not result:
        print(f"No prompt_seed found for {task_name}/{dataset_source} with exactly {limit_responses} responses")
        return None

    group = result[0]
    docs = group["docs"]

    return {
        "group_id": group["_id"],
        "docs": docs,
        "count": len(docs)
    }


def build_group_key(doc: Dict[str, Any], group_id: Dict[str, Any]) -> Dict[str, Any]:
    """Build group key from a document and group_id."""
    return {
        "task_type": doc.get("task_name"),
        "dataset_source": doc.get("dataset_source"),
        "prompt_seed": group_id.get("prompt_seed"),
        "input_text": doc.get("input_text"),
    }


def extract_responses(docs: List[Dict[str, Any]], task_name: str) -> List[str]:
    """Extract responses from documents based on task type."""
    responses = []

    for doc in docs:
        if task_name == "sentiment_analysis":
            # Use parsed_answer for sentiment analysis
            response = doc.get("parsed_answer")
        elif task_name == "explorative_coding":
            # Use raw_answer for explorative coding
            response = doc.get("raw_answer")
        else:
            # Fallback: try parsed_answer first, then raw_answer
            response = doc.get("parsed_answer") or doc.get("raw_answer")

        if response:
            responses.append(str(response))

    return responses


def infer_reference_text(docs: List[Dict[str, Any]]) -> Optional[str]:
    """Infer reference text from documents."""
    try:
        refs = [d.get("reference_answer") for d in docs if d.get("reference_answer")]
        if refs:
            from collections import Counter
            return Counter(refs).most_common(1)[0][0]
    except Exception:
        pass
    return None


def print_detailed_luq_results(result: Dict[str, Any], method_name: str):
    """Print detailed LUQ results in a formatted way."""
    print(f"\n=== Detailed {method_name} Results ===")
    
    # Basic metrics
    print(f"Overall Uncertainty Score: {result.get('uncertainty_score', 'N/A'):.4f}")
    print(f"Overall Consistency: {result.get('overall_consistency', 'N/A'):.4f}")
    print(f"Number of Responses: {result.get('num_responses', 'N/A')}")
    print(f"Method Type: {result.get('method_type', 'N/A')}")
    print(f"Abridged Mode: {result.get('abridged', 'N/A')}")
    
    # Per-sample metrics
    if 'luq_scores_per_sample' in result:
        luq_scores = result['luq_scores_per_sample']
        print(f"\nUncertainty Scores per Sample: {luq_scores}")
        import numpy as np
        luq_array = np.array(luq_scores)
        print(f"  Mean: {luq_array.mean():.4f}, Std: {luq_array.std():.4f}")
        print(f"  Min: {luq_array.min():.4f}, Max: {luq_array.max():.4f}")

    if 'consistency_scores_per_sample' in result:
        consistency_scores = result['consistency_scores_per_sample']
        print(f"\nConsistency Scores per Sample: {consistency_scores}")
        import numpy as np
        consistency_array = np.array(consistency_scores)
        print(f"  Mean: {consistency_array.mean():.4f}, Std: {consistency_array.std():.4f}")
    
    # Sentence-level metrics
    if 'sentence_uncertainties' in result:
        sentence_uncertainties = result['sentence_uncertainties']
        print(f"\nSentence Uncertainties per Response:")
        import numpy as np
        for i, sent_unc in enumerate(sentence_uncertainties):
            sent_array = np.array(sent_unc)
            print(f"  Response {i}: {sent_unc} (mean: {sent_array.mean():.4f})")
    
    if 'num_sentences_per_response' in result:
        num_sentences = result['num_sentences_per_response']
        print(f"\nNumber of Sentences per Response: {num_sentences}")
        print(f"  Total sentences: {sum(num_sentences)}, Average: {sum(num_sentences)/len(num_sentences):.1f}")
    
    # Sample responses and sentences
    if 'sentences_per_response' in result:
        sentences_per_response = result['sentences_per_response']
        print(f"\nSample Sentence Breakdown:")
        for i, sentences in enumerate(sentences_per_response[:3]):  # Show first 3 responses
            print(f"  Response {i} ({len(sentences)} sentences): {sentences}")
    
    print("=" * 50)


def test_luq_methods():
    """Main test function for LUQ methods."""
    # Connect to MongoDB
    client = MongoClient('mongodb://localhost:27017/')
    db = client['LLM-UQ']
    col_src = db['response_collections']
    col_out = db['UQ_results_test']
    
    # Create index for output collection
    try:
        col_out.create_index([
            ("group_key.task_type", 1), ("group_key.dataset_source", 1), 
            ("group_key.prompt_variant", 1), ("group_key.prompt_seed", 1), 
            ("group_key.prompt_index", 1), ("method.method_name", 1)
        ], name="uq_test_group_method")
    except Exception:
        pass
    
    # Initialize LUQ methods
    methods = [
        ("LUQ_Binary", LUQUQ(method="binary", verbose=False)),
        ("LUQ_Multiclass", LUQUQ(method="multiclass", verbose=False)),
        ("LUQ_Binary_Abridged", LUQUQ(method="binary", abridged=True, verbose=False)),
    ]
    
    # Test cases: (task_name, dataset_source)
    test_cases = [
        ("sentiment_analysis", "twitter_sentiment"),
        ("explorative_coding", "pytorch_commits")
    ]
    
    all_results = []
    
    print("Starting LUQ method testing with ECC data...")
    
    for task_name, dataset_source in test_cases:
        print(f"\n{'='*60}")
        print(f"Testing {task_name} / {dataset_source}")
        print(f"{'='*60}")
        
        # Get sample group with 40 responses
        group_data = get_sample_group(col_src, task_name, dataset_source, limit_responses=40)
        if not group_data:
            continue
            
        docs = group_data["docs"]
        group_key = build_group_key(docs[0], group_data["group_id"])

        # Extract responses based on task type
        responses = extract_responses(docs, task_name)
        if len(responses) < 2:
            print(f"Not enough valid responses: {len(responses)}")
            continue

        print(f"Found {len(responses)} responses for testing")
        
        # Get reference text
        reference_text = infer_reference_text(docs)
        print(f"Reference text: {reference_text}")
        
        # Print sample input and responses
        print(f"Input text: {group_key['input_text'][:100]}...")
        print(f"Sample responses (first 3): {responses[:3]}")
        
        # Test each LUQ method
        for method_name, method_instance in methods:
            print(f"\n{'-'*40}")
            print(f"Testing {method_name}")
            print(f"{'-'*40}")
            
            try:
                # Compute uncertainty
                result = method_instance.compute_uncertainty(responses)
                
                # Print detailed results
                print_detailed_luq_results(result, method_name)
                
                # Extract uncertainty score
                uq_value = result.get("uncertainty_score")
                
                print(f"\n✓ {method_name} UQ Score: {uq_value:.4f}")
                
                # Create record for MongoDB
                record = {
                    "group_key": group_key,
                    "method": {
                        "method_name": method_name,
                        "model_category": "LUQ",
                        "method_params": {
                            "method_type": result.get("method_type"),
                            "abridged": result.get("abridged"),
                            "model_name": result.get("metadata", {}).get("model_name")
                        },
                    },
                    "outputs": {
                        "uq_value": uq_value,
                        "metrics": {k: v for k, v in result.items() 
                                  if k not in ("uncertainty_score",)}
                    },
                    "meta": {
                        "n_responses": len(responses),
                        "test_type": "luq_methods_test"
                    },
                    "timestamps": {"created_at": datetime.now(timezone.utc)}
                }
                
                # Save to MongoDB
                col_out.insert_one(record)
                all_results.append(record)
                
                print(f"✓ {method_name} completed and saved to MongoDB")
                
            except Exception as e:
                print(f"✗ {method_name} failed: {str(e)}")
                import traceback
                traceback.print_exc()
                
                # Create error record
                error_record = {
                    "group_key": group_key,
                    "method": {"method_name": method_name},
                    "outputs": {"uq_value": None, "metrics": {"error": str(e)}},
                    "meta": {"n_responses": len(responses), "test_type": "luq_methods_test"},
                    "timestamps": {"created_at": datetime.now(timezone.utc)}
                }
                col_out.insert_one(error_record)
                all_results.append(error_record)
    
    # Save results to JSON file
    output_file = "luq_methods_test_results.json"
    with open(output_file, "w") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n{'='*60}")
    print(f"LUQ Test Complete")
    print(f"{'='*60}")
    print(f"Total results: {len(all_results)}")
    print(f"Results saved to MongoDB (UQ_results_test) and {output_file}")
    
    # Print summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    for result in all_results:
        method_name = result["method"]["method_name"]
        task_type = result["group_key"]["task_type"]
        dataset_source = result["group_key"]["dataset_source"]
        uq_value = result["outputs"]["uq_value"]
        error = result["outputs"]["metrics"].get("error")
        
        if error:
            print(f"❌ {method_name} on {task_type}/{dataset_source}: ERROR - {error}")
        else:
            print(f"✅ {method_name} on {task_type}/{dataset_source}: UQ = {uq_value:.4f}")


if __name__ == "__main__":
    test_luq_methods()
