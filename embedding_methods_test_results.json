[{"group_key": {"task_type": "sentiment_analysis", "dataset_source": "twitter_sentiment", "prompt_seed": 164, "input_text": "Sexual liberation, gay marriage, no fault divorce, chem sex, child sex victimhood, false rape accusations and the h… https://t.co/bH05TaOFR6"}, "method": {"method_name": "Embedding_<PERSON>wen", "model_category": null, "method_params": {}}, "outputs": {"uq_value": 0.025795578956604004, "metrics": {"avg_candidate_distance": 0.025795578956604004, "avg_distance_to_reference": 0.031632065773010254, "num_responses": 40, "method": "Embedding_<PERSON>wen"}}, "meta": {"n_responses": 40, "test_type": "embedding_methods_test"}, "timestamps": {"created_at": "2025-08-12 03:48:30.826840+00:00"}, "_id": "689ab98e0f1c878f5e136c10"}, {"group_key": {"task_type": "sentiment_analysis", "dataset_source": "twitter_sentiment", "prompt_seed": 164, "input_text": "Sexual liberation, gay marriage, no fault divorce, chem sex, child sex victimhood, false rape accusations and the h… https://t.co/bH05TaOFR6"}, "method": {"method_name": "E5", "model_category": null, "method_params": {}}, "outputs": {"uq_value": 0.02917414903640747, "metrics": {"avg_candidate_distance": 0.02917414903640747, "avg_distance_to_reference": 0.038664937019348145, "num_responses": 40, "method": "E5"}}, "meta": {"n_responses": 40, "test_type": "embedding_methods_test"}, "timestamps": {"created_at": "2025-08-12 03:48:37.320194+00:00"}, "_id": "689ab9950f1c878f5e136c11"}, {"group_key": {"task_type": "explorative_coding", "dataset_source": "pytorch_commits", "prompt_seed": 88, "input_text": "Revert D21302691: [pytorch][PR] Implement cusparse Descriptor class and clean up cusparse code\n\nTest Plan: revert-hammer\n\nDifferential Revision:\nD21302691\n\nOriginal commit changeset: ecbb4063466c\n\nfbshipit-source-id: 56ae47273691a12cc8d96635fb4ad9d09080ccc9"}, "method": {"method_name": "Embedding_<PERSON>wen", "model_category": null, "method_params": {}}, "outputs": {"uq_value": 0.1375976800918579, "metrics": {"avg_candidate_distance": 0.1375976800918579, "avg_distance_to_reference": null, "num_responses": 40, "method": "Embedding_<PERSON>wen"}}, "meta": {"n_responses": 40, "test_type": "embedding_methods_test"}, "timestamps": {"created_at": "2025-08-12 03:48:37.850782+00:00"}, "_id": "689ab9950f1c878f5e136c12"}, {"group_key": {"task_type": "explorative_coding", "dataset_source": "pytorch_commits", "prompt_seed": 88, "input_text": "Revert D21302691: [pytorch][PR] Implement cusparse Descriptor class and clean up cusparse code\n\nTest Plan: revert-hammer\n\nDifferential Revision:\nD21302691\n\nOriginal commit changeset: ecbb4063466c\n\nfbshipit-source-id: 56ae47273691a12cc8d96635fb4ad9d09080ccc9"}, "method": {"method_name": "E5", "model_category": null, "method_params": {}}, "outputs": {"uq_value": 0.039789676666259766, "metrics": {"avg_candidate_distance": 0.039789676666259766, "avg_distance_to_reference": null, "num_responses": 40, "method": "E5"}}, "meta": {"n_responses": 40, "test_type": "embedding_methods_test"}, "timestamps": {"created_at": "2025-08-12 03:48:38.232117+00:00"}, "_id": "689ab9960f1c878f5e136c13"}]