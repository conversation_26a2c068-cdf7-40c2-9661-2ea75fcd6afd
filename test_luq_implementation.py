#!/usr/bin/env python3
"""
Test script for LUQ implementation
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_luq_import():
    """Test that LUQ can be imported"""
    try:
        from uq_methods.implementations.luq import LUQUQ
        print("✓ LUQ import successful")
        return True
    except ImportError as e:
        print(f"✗ LUQ import failed: {e}")
        return False

def test_luq_initialization():
    """Test LUQ initialization"""
    try:
        from uq_methods.implementations.luq import LUQUQ
        luq = LUQUQ(method="binary", verbose=True)
        print("✓ LUQ initialization successful")
        return True
    except Exception as e:
        print(f"✗ LUQ initialization failed: {e}")
        return False

def test_luq_interface():
    """Test that LUQ implements the required interface"""
    try:
        from uq_methods.implementations.luq import LUQUQ
        from uq_methods.base import BaseUQMethod

        # Check inheritance
        assert issubclass(LUQUQ, BaseUQMethod)
        print("✓ LUQ properly inherits from BaseUQMethod")

        # Check required methods exist
        required_methods = ['compute_uncertainty', 'get_required_samples', 'get_method_name']
        for method in required_methods:
            assert hasattr(LUQUQ, method)
            print(f"✓ LUQ has required method: {method}")

        return True
    except Exception as e:
        print(f"✗ LUQ interface test failed: {e}")
        return False

def test_luq_structure():
    """Test the structure and methods of LUQ class"""
    try:
        from uq_methods.implementations.luq import LUQUQ

        # Test class attributes and methods
        expected_methods = [
            '__init__', '_split_into_sentences', '_compute_nli_score',
            'compute_uncertainty', '_compute_luq_score',
            'get_required_samples', 'get_method_name', '__str__'
        ]

        for method in expected_methods:
            assert hasattr(LUQUQ, method)
            print(f"✓ LUQ has method: {method}")

        return True
    except Exception as e:
        print(f"✗ LUQ structure test failed: {e}")
        return False

def test_luq_functionality():
    """Test basic LUQ functionality with sample data"""
    try:
        from uq_methods.implementations.luq import LUQUQ

        # Create LUQ instance
        luq = LUQUQ(method="binary", verbose=False)

        # Test with sample responses
        responses = [
            "Michael Alan Weiner was born March 31, 1942. He is an American radio host.",
            "Michael Alan Weiner was born January 13, 1960. He works at The New York Times.",
            "Michael Alan Weiner was born March 31, 1942. He obtained his PhD from MIT."
        ]

        # Compute uncertainty
        result = luq.compute_uncertainty(responses)

        # Check result structure
        assert isinstance(result, dict)
        assert "uncertainty_score" in result
        assert "method" in result
        assert isinstance(result["uncertainty_score"], (int, float))

        print(f"✓ LUQ functionality test passed")
        print(f"  Uncertainty score: {result['uncertainty_score']:.4f}")
        print(f"  Method: {result['method']}")

        return True
    except Exception as e:
        print(f"✗ LUQ functionality test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing LUQ Implementation")
    print("=" * 50)

    tests = [
        test_luq_import,
        test_luq_interface,
        test_luq_structure,
        test_luq_initialization,
        test_luq_functionality,
    ]

    results = []
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        results.append(test())

    print("\n" + "=" * 50)
    print(f"Test Results: {sum(results)}/{len(results)} passed")

    if sum(results) >= 4:
        print("✓ LUQ implementation looks good!")
        print("Note: Uses NLI-based consistency checking for uncertainty quantification")
    else:
        print("✗ LUQ implementation has issues")

if __name__ == "__main__":
    main()
