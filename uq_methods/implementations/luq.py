import sys
import os
import numpy as np
import logging
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from uq_methods.base import BaseUQMethod
from core.nli_shared import get_nli_calculator

log = logging.getLogger(__name__)


class LUQUQ(BaseUQMethod):
    """
    Estimates the sequence-level uncertainty of a language model following the method of
    "LUQ: Long-text Uncertainty Quantification for LLMs" as provided in the paper
    https://aclanthology.org/2024.emnlp-main.299.pdf.

    This implementation focuses on uncertainty computation using NLI-based consistency checking
    between different response samples, following the LUQ methodology.
    """

    def __init__(
        self,
        model_name: str = "microsoft/deberta-large-mnli",
        method: str = "binary",
        abridged: bool = False,
        verbose: bool = False
    ):
        """
        Initialize LUQ uncertainty quantification method.

        Parameters:
            model_name (str): NLI model name for consistency checking.
            method (str): The method to use. "binary" or "multiclass" are supported.
            abridged (bool): If True, return score of first sentence only for quicker results.
            verbose (bool): Whether to print debug information.
        """
        self.model_name = model_name
        self.method = method
        self.abridged = abridged
        self.verbose = verbose

        # Initialize NLI calculator for consistency checking
        self.nli_calc = get_nli_calculator(model_name)

        # Set up text mappings based on method
        if self.method == "binary":
            # For binary: entailment=1 (supported), contradiction=0 (not supported)
            self.text_mapping = {'entail': 1, 'contradict': 0, 'neutral': 0.5}
        elif self.method == "multiclass":
            # For multiclass: entailment=1 (supported), contradiction=0 (refuted), neutral=-1 (not mentioned)
            self.text_mapping = {'entail': 1, 'contradict': 0, 'neutral': -1}
        else:
            raise ValueError(f"Method {method} not supported")

    def _split_into_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences using simple period-based splitting.
        Can be enhanced with more sophisticated sentence segmentation.

        Parameters:
            text (str): Input text to split

        Returns:
            List[str]: List of sentences
        """
        # Simple sentence splitting - can be improved with spacy or nltk
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        return sentences

    def _compute_nli_score(self, premise: str, hypothesis: str) -> float:
        """
        Compute NLI-based consistency score between premise and hypothesis.

        Parameters:
            premise (str): Context text (premise)
            hypothesis (str): Sentence to check (hypothesis)

        Returns:
            float: Consistency score based on NLI result
        """
        nli_result = self.nli_calc.compute_nli_scores_cached(premise, hypothesis)

        if self.method == "binary":
            # Binary: entailment indicates support
            if nli_result.entailment > max(nli_result.contradiction, nli_result.neutral):
                return self.text_mapping['entail']
            elif nli_result.contradiction > nli_result.neutral:
                return self.text_mapping['contradict']
            else:
                return self.text_mapping['neutral']

        elif self.method == "multiclass":
            # Multiclass: distinguish between entailment, contradiction, and neutral
            max_score = max(nli_result.entailment, nli_result.contradiction, nli_result.neutral)
            if nli_result.entailment == max_score:
                return self.text_mapping['entail']
            elif nli_result.contradiction == max_score:
                return self.text_mapping['contradict']
            else:
                return self.text_mapping['neutral']

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        Compute LUQ uncertainty score for the given responses.

        Parameters:
            responses (List[str]): List of response texts to evaluate

        Returns:
            Dict[str, Any]: Dictionary containing uncertainty scores and detailed metrics
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 1.0,
                "error": "Need at least 2 responses for LUQ computation",
                "method": "LUQ"
            }

        try:
            # Split responses into sentences
            sentences_list = []
            for response in responses:
                sentences = self._split_into_sentences(response)
                sentences_list.append(sentences)

            if self.verbose:
                log.debug(f"Split responses into sentences: {sentences_list}")

            # Compute detailed LUQ scores
            detailed_results = self._compute_luq_detailed(sentences_list)

            return {
                "uncertainty_score": detailed_results["overall_uncertainty"],
                "method": "LUQ",
                "num_responses": len(responses),
                "method_type": self.method,
                "abridged": self.abridged,

                # Detailed LUQ metrics
                "luq_scores_per_sample": detailed_results["luq_scores_per_sample"],
                "sentence_uncertainties": detailed_results["sentence_uncertainties"],
                "consistency_scores_per_sample": detailed_results["consistency_scores_per_sample"],
                "sentence_consistency_matrix": detailed_results["sentence_consistency_matrix"],
                "overall_consistency": detailed_results["overall_consistency"],
                "num_sentences_per_response": detailed_results["num_sentences_per_response"],

                # Original responses and sentences
                "original_responses": responses,
                "sentences_per_response": sentences_list,

                "metadata": {
                    "model_name": self.model_name,
                    "method": self.method,
                    "abridged": self.abridged,
                    "verbose": self.verbose,
                    "nli_system": "shared_cached"
                }
            }

        except Exception as e:
            log.error(f"Error computing LUQ uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "LUQ"
            }

    def _compute_luq_detailed(self, sentences_list: List[List[str]]) -> Dict[str, Any]:
        """
        Internal method to compute detailed LUQ scores following the original algorithm.

        This method implements the core LUQ logic and returns comprehensive results:
        1. For each response sample, use other samples as context
        2. Check consistency of each sentence against other samples using NLI
        3. Compute average consistency scores
        4. Return detailed uncertainty metrics including per-sentence scores

        Parameters:
            sentences_list (List[List[str]]): List of sentence lists for each response

        Returns:
            Dict[str, Any]: Detailed LUQ results including arrays and matrices
        """
        all_samples = sentences_list
        num_samples = len(all_samples)

        # Initialize result containers
        luq_scores_per_sample = np.zeros(num_samples)
        sentence_uncertainties = []  # List of arrays, one per sample
        consistency_scores_per_sample = np.zeros(num_samples)
        sentence_consistency_matrix = []  # Detailed consistency matrix for each sample
        num_sentences_per_response = [len(sample) for sample in all_samples]

        for index, item in enumerate(all_samples):
            # Get other samples as context (join sentences into full text)
            context_samples = [" ".join(sample) for sample in all_samples if sample != item]

            num_sentences = len(item)
            num_contexts = len(context_samples)

            if num_contexts == 0 or num_sentences == 0:
                luq_scores_per_sample[index] = 0.5  # Default uncertainty
                sentence_uncertainties.append(np.array([0.5] * max(1, num_sentences)))
                consistency_scores_per_sample[index] = 0.5
                sentence_consistency_matrix.append(np.array([[0.5]]))
                continue

            # Consistency scores matrix: [num_sentences x num_contexts]
            scores = np.zeros((num_sentences, num_contexts))

            # For each sentence in current sample
            for sent_i in range(num_sentences):
                sentence = item[sent_i]

                # Check consistency against each context sample
                for context_i, context in enumerate(context_samples):
                    # Clean context text
                    context = context.replace("\n", " ").strip()

                    # Compute NLI-based consistency score
                    consistency_score = self._compute_nli_score(context, sentence)
                    scores[sent_i, context_i] = consistency_score

                    if self.verbose:
                        log.debug(f"Sample {index}, Sentence {sent_i}: '{sentence}' vs Context {context_i}: {consistency_score}")

            # Store the raw consistency matrix for this sample
            sentence_consistency_matrix.append(scores.copy())

            # Filter out -1 values (not mentioned in multiclass) and compute mean
            if self.method == "multiclass":
                scores_filtered = np.ma.masked_equal(scores, -1)
                scores_per_sentence = scores_filtered.mean(axis=-1)
                scores_per_sentence = np.where(scores_per_sentence.mask, 0, scores_per_sentence)
            else:
                scores_per_sentence = scores.mean(axis=-1)

            # Calculate the average consistency score for this sample
            sample_consistency = scores_per_sentence.mean()
            consistency_scores_per_sample[index] = sample_consistency

            # Convert consistency to uncertainty for each sentence
            sentence_uncertainty_array = 1.0 - scores_per_sentence
            sentence_uncertainties.append(sentence_uncertainty_array)

            # Sample-level uncertainty
            sample_uncertainty = 1.0 - sample_consistency
            luq_scores_per_sample[index] = sample_uncertainty

            if self.verbose:
                log.debug(f"Sample {index} - Consistency per sentence: {scores_per_sentence}")
                log.debug(f"Sample {index} - Uncertainty per sentence: {sentence_uncertainty_array}")
                log.debug(f"Sample {index} - Average consistency: {sample_consistency}")
                log.debug(f"Sample {index} - Sample uncertainty: {sample_uncertainty}")

            # If abridged mode, return early with first sentence score
            if self.abridged:
                first_sentence_uncertainty = sentence_uncertainty_array[0] if len(sentence_uncertainty_array) > 0 else 0.5
                return {
                    "overall_uncertainty": float(first_sentence_uncertainty),
                    "luq_scores_per_sample": [float(first_sentence_uncertainty)],
                    "sentence_uncertainties": [[float(first_sentence_uncertainty)]],
                    "consistency_scores_per_sample": [float(1.0 - first_sentence_uncertainty)],
                    "sentence_consistency_matrix": [(scores[:1, :] if scores.size > 0 else np.array([[0.5]])).tolist()],
                    "overall_consistency": float(1.0 - first_sentence_uncertainty),
                    "num_sentences_per_response": [1]
                }

        # Calculate overall metrics
        overall_consistency = consistency_scores_per_sample.mean()
        overall_uncertainty = luq_scores_per_sample.mean()

        if self.verbose:
            log.debug(f"Overall consistency: {overall_consistency}")
            log.debug(f"Overall uncertainty: {overall_uncertainty}")
            log.debug(f"LUQ scores per sample: {luq_scores_per_sample}")

        return {
            "overall_uncertainty": float(overall_uncertainty),
            "luq_scores_per_sample": luq_scores_per_sample.tolist(),
            "sentence_uncertainties": [arr.tolist() for arr in sentence_uncertainties],
            "consistency_scores_per_sample": consistency_scores_per_sample.tolist(),
            "sentence_consistency_matrix": [arr.tolist() for arr in sentence_consistency_matrix],
            "overall_consistency": float(overall_consistency),
            "num_sentences_per_response": num_sentences_per_response
        }

    def _compute_luq_score(self, sentences_list: List[List[str]]) -> float:
        """
        Simplified method to compute LUQ score (for backward compatibility).

        Parameters:
            sentences_list (List[List[str]]): List of sentence lists for each response

        Returns:
            float: LUQ uncertainty score (higher = more uncertain)
        """
        detailed_results = self._compute_luq_detailed(sentences_list)
        return detailed_results["overall_uncertainty"]

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 2

    def get_method_name(self) -> str:
        """Get the method name."""
        return f"LUQ_{self.method.capitalize()}"

    def __str__(self):
        return f"LUQ_{self.method}"