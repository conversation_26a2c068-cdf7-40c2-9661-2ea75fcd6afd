"""
LofreeCP (Logit-free Conformal Prediction) uncertainty quantification method
Based on "API Is Enough: Conformal Prediction for Large Language Models Without Logit-Access"
https://arxiv.org/abs/2403.01216

This implementation provides uncertainty quantification for LLMs without requiring logit access,
using a combination of coarse-grained (frequency) and fine-grained (normalized entropy, semantic similarity)
uncertainty measures.
"""
import sys
import os
import numpy as np
import logging
from typing import List, Dict, Any, Optional
from collections import Counter
import re

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from uq_methods.base import BaseUQMethod

# Try to import embedding functionality for semantic similarity
try:
    from core.embedding_cache import get_embedding_encoder
    EMBEDDING_AVAILABLE = True
except ImportError:
    EMBEDDING_AVAILABLE = False
    logging.warning("Embedding functionality not available for LofreeCP semantic similarity")

log = logging.getLogger(__name__)


class LofreeCPUQ(BaseUQMethod):
    """
    LofreeCP (Logit-free Conformal Prediction) uncertainty quantification method.

    This method implements the LofreeCP algorithm which combines:
    1. Frequency (F): Coarse-grained uncertainty based on response frequency
    2. Normalized Entropy (NE): Fine-grained prompt-wise uncertainty
    3. Semantic Similarity (SS): Fine-grained response-wise uncertainty

    The nonconformity score is computed as:
    N = -F + λ1 * NE - λ2 * SS

    Where:
    - F is the frequency of the response divided by total samples
    - NE is the normalized entropy of the response distribution
    - SS is the semantic similarity to the most frequent response
    - λ1, λ2 are hyperparameters controlling the balance
    """

    def __init__(
        self,
        lambda1: float = 1.0,
        lambda2: float = 1.0,
        embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2",
        verbose: bool = False
    ):
        """
        Initialize LofreeCP uncertainty quantification method.

        Parameters:
            lambda1 (float): Weight for normalized entropy term
            lambda2 (float): Weight for semantic similarity term
            embedding_model (str): Model name for computing semantic similarity
            verbose (bool): Whether to print debug information
        """
        self.lambda1 = lambda1
        self.lambda2 = lambda2
        self.embedding_model = embedding_model
        self.verbose = verbose

        # Initialize embedding encoder if available
        self.embedding_encoder = None
        if EMBEDDING_AVAILABLE:
            try:
                self.embedding_encoder = get_embedding_encoder(embedding_model)
            except Exception as e:
                log.warning(f"Failed to initialize embedding encoder: {e}")
                self.embedding_encoder = None

    def _preprocess_response(self, response: str) -> str:
        """
        Preprocess response text for analysis.

        Parameters:
            response (str): Raw response text

        Returns:
            str: Cleaned response text
        """
        if not response:
            return ""

        # Convert to lowercase and remove extra whitespace
        response = response.lower().strip()

        # Remove common stop phrases that don't affect semantic meaning
        stop_phrases = ["the answer is", "answer:", "i think", "in my opinion", "based on"]
        for phrase in stop_phrases:
            response = response.replace(phrase, "")

        # Remove extra whitespace and normalize punctuation
        response = re.sub(r'\s+', ' ', response)
        response = re.sub(r'[^\w\s]', ' ', response)
        response = response.strip()

        return response

    def _compute_frequency(self, responses: List[str], target_response: str) -> float:
        """
        Compute frequency of target response in the response list.

        Parameters:
            responses (List[str]): List of all responses
            target_response (str): Response to compute frequency for

        Returns:
            float: Frequency of target response (0 to 1)
        """
        if not responses:
            return 0.0

        # Preprocess all responses for fair comparison
        processed_responses = [self._preprocess_response(r) for r in responses]
        processed_target = self._preprocess_response(target_response)

        count = processed_responses.count(processed_target)
        return count / len(responses)

    def _compute_normalized_entropy(self, responses: List[str]) -> float:
        """
        Compute normalized entropy of response distribution.

        Parameters:
            responses (List[str]): List of responses

        Returns:
            float: Normalized entropy (0 to 1)
        """
        if not responses:
            return 0.0

        # Preprocess responses and count frequencies
        processed_responses = [self._preprocess_response(r) for r in responses]
        response_counts = Counter(processed_responses)

        # Calculate probabilities
        total = len(responses)
        probabilities = [count / total for count in response_counts.values()]

        # Calculate entropy
        entropy = -sum(p * np.log(p) for p in probabilities if p > 0)

        # Normalize by maximum possible entropy
        max_entropy = np.log(len(responses)) if len(responses) > 1 else 1.0
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.0

        return normalized_entropy