"""
LofreeCP (Logit-free Conformal Prediction) uncertainty quantification method
Based on "API Is Enough: Conformal Prediction for Large Language Models Without Logit-Access"
https://arxiv.org/abs/2403.01216

This implementation provides uncertainty quantification for LLMs without requiring logit access,
using a combination of coarse-grained (frequency) and fine-grained (normalized entropy, semantic similarity)
uncertainty measures.
"""
import sys
import os
import numpy as np
import logging
import math
import string
from typing import List, Dict, Any
from collections import defaultdict

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from uq_methods.base import BaseUQMethod

# Try to import embedding functionality for semantic similarity
try:
    from core.embedding_cache import get_embedding_encoder
    EMBEDDING_AVAILABLE = True
except ImportError:
    EMBEDDING_AVAILABLE = False
    logging.warning("Embedding functionality not available for LofreeCP semantic similarity")

log = logging.getLogger(__name__)


class LofreeCPUQ(BaseUQMethod):
    """
    LofreeCP (Logit-free Conformal Prediction) uncertainty quantification method.

    This method implements the LofreeCP algorithm which combines:
    1. Frequency (F): Coarse-grained uncertainty based on response frequency
    2. Normalized Entropy (NE): Fine-grained prompt-wise uncertainty
    3. Semantic Similarity (SS): Fine-grained response-wise uncertainty

    The nonconformity score is computed as:
    N = -F + λ1 * NE - λ2 * SS

    Where:
    - F is the frequency of the response divided by total samples
    - NE is the normalized entropy of the response distribution
    - SS is the semantic similarity to the most frequent response
    - λ1, λ2 are hyperparameters controlling the balance
    """

    def __init__(
        self,
        lambda1: float = 1.0,
        lambda2: float = 1.0,
        embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2",
        verbose: bool = False
    ):
        """
        Initialize LofreeCP uncertainty quantification method.

        Parameters:
            lambda1 (float): Weight for normalized entropy term
            lambda2 (float): Weight for semantic similarity term
            embedding_model (str): Model name for computing semantic similarity
            verbose (bool): Whether to print debug information
        """
        self.lambda1 = lambda1
        self.lambda2 = lambda2
        self.embedding_model = embedding_model
        self.verbose = verbose

        # Initialize embedding encoder if available
        self.embedding_encoder = None
        if EMBEDDING_AVAILABLE:
            try:
                self.embedding_encoder = get_embedding_encoder(embedding_model)
            except Exception as e:
                log.warning(f"Failed to initialize embedding encoder: {e}")
                self.embedding_encoder = None

    def _remove_punctuation(self, input_string: str) -> str:
        """Remove punctuation from string."""
        translator = str.maketrans("", "", string.punctuation)
        return input_string.translate(translator)

    def _remove_articles(self, input_string: str) -> str:
        """Remove articles (a, an, the) from string."""
        articles = ['a', 'an', 'the']
        words = input_string.split()
        result = ' '.join(word for word in words if word.lower() not in articles)
        return result

    def _remove_duplicate_whitespace(self, input_string: str) -> str:
        """Remove duplicate whitespace from string."""
        return ' '.join(input_string.split())

    def _preprocess_response(self, response: str) -> str:
        """
        Preprocess response text for analysis following the reference implementation.

        Parameters:
            response (str): Raw response text

        Returns:
            str: Cleaned response text
        """
        if not response:
            return ""

        # Apply preprocessing steps from reference code
        processed = response.lower()
        processed = self._remove_punctuation(processed)
        processed = self._remove_articles(processed)
        processed = self._remove_duplicate_whitespace(processed)

        return processed.strip()

    def _responses_to_frequency_dict(self, responses: List[str]) -> Dict[str, int]:
        """
        Convert list of responses to frequency dictionary.

        Parameters:
            responses (List[str]): List of responses

        Returns:
            Dict[str, int]: Dictionary mapping response to frequency, sorted by frequency
        """
        # Preprocess responses
        processed_responses = [self._preprocess_response(r) for r in responses if r.strip()]

        # Count frequencies
        element_frequency = defaultdict(int)
        for response in processed_responses:
            if response:  # Only count non-empty responses
                element_frequency[response] += 1

        # Sort by frequency (descending)
        sorted_frequency = dict(sorted(element_frequency.items(), key=lambda item: item[1], reverse=True))
        return sorted_frequency

    def _compute_frequency(self, responses: List[str], target_response: str) -> float:
        """
        Compute frequency of target response in the response list.

        Parameters:
            responses (List[str]): List of all responses
            target_response (str): Response to compute frequency for

        Returns:
            float: Frequency of target response (0 to 1)
        """
        if not responses:
            return 0.0

        # Preprocess all responses for fair comparison
        processed_responses = [self._preprocess_response(r) for r in responses]
        processed_target = self._preprocess_response(target_response)

        count = processed_responses.count(processed_target)
        return count / len(responses)

    def _compute_normalized_entropy(self, freq_dict: Dict[str, int]) -> float:
        """
        Compute normalized entropy of response distribution from frequency dictionary.

        Parameters:
            freq_dict (Dict[str, int]): Dictionary mapping response to frequency

        Returns:
            float: Normalized entropy (0 to 1)
        """
        if not freq_dict:
            return 0.0

        total_frequency = sum(freq_dict.values())
        if total_frequency <= 1:
            return 0.0

        # Calculate entropy following reference implementation
        numerator = 0
        for _, value in freq_dict.items():
            if value > 0:
                prob = value / total_frequency
                numerator += -prob * math.log(prob)

        # Normalize by log of total frequency
        normalized_entropy = numerator / math.log(total_frequency)

        return normalized_entropy

    def _compute_semantic_similarity(self, response1: str, response2: str) -> float:
        """
        Compute semantic similarity between two responses.

        Parameters:
            response1 (str): First response
            response2 (str): Second response

        Returns:
            float: Semantic similarity score (0 to 1)
        """
        if not response1 or not response2:
            return 0.0

        if response1 == response2:
            return 1.0

        # If embedding encoder is available, use it
        if self.embedding_encoder is not None:
            try:
                # Get embeddings - try different methods based on encoder type
                if hasattr(self.embedding_encoder, 'encode'):
                    emb1 = self.embedding_encoder.encode([response1])[0]
                    emb2 = self.embedding_encoder.encode([response2])[0]
                elif hasattr(self.embedding_encoder, 'get_embedding'):
                    emb1 = self.embedding_encoder.get_embedding(response1)
                    emb2 = self.embedding_encoder.get_embedding(response2)
                else:
                    raise AttributeError("Encoder has no supported embedding method")

                # Compute cosine similarity
                dot_product = np.dot(emb1, emb2)
                norm1 = np.linalg.norm(emb1)
                norm2 = np.linalg.norm(emb2)

                if norm1 == 0 or norm2 == 0:
                    return 0.0

                similarity = dot_product / (norm1 * norm2)
                return max(0.0, similarity)  # Ensure non-negative

            except Exception as e:
                if self.verbose:
                    log.warning(f"Failed to compute semantic similarity: {e}")

        # Fallback: simple word overlap similarity
        words1 = set(response1.lower().split())
        words2 = set(response2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        return intersection / union if union > 0 else 0.0

    def _compute_lofreecp_scores(self, freq_dict: Dict[str, int]) -> Dict[str, float]:
        """
        Compute LofreeCP nonconformity scores for all responses in frequency dictionary.

        Parameters:
            freq_dict (Dict[str, int]): Dictionary mapping response to frequency

        Returns:
            Dict[str, float]: Dictionary mapping response to nonconformity score
        """
        if not freq_dict:
            return {}

        total_frequency = sum(freq_dict.values())
        if total_frequency == 0:
            return {key: 10.0 for key in freq_dict.keys()}

        # Compute normalized entropy for this response set
        normalized_entropy = self._compute_normalized_entropy(freq_dict)

        # Get the most frequent response (rank 1)
        rank_1_response = next(iter(freq_dict.keys())) if freq_dict else ""

        # Compute scores for each response
        scores = {}
        for rank, (response, frequency) in enumerate(freq_dict.items()):
            # Base score: 10 - frequency_ratio * 10
            frequency_ratio = frequency / total_frequency
            base_score = 10 - frequency_ratio * 10

            # Add normalized entropy term (weighted by lambda1)
            entropy_term = normalized_entropy / 2 * self.lambda1

            # Compute semantic similarity term (weighted by lambda2)
            similarity_term = 0.0
            if rank > 0 and response != rank_1_response:  # Not the most frequent response
                similarity = self._compute_semantic_similarity(response, rank_1_response)
                similarity_term = similarity * self.lambda2

            # Final nonconformity score: base + entropy - similarity
            final_score = base_score + entropy_term - similarity_term
            scores[response] = final_score

        return scores

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        Compute LofreeCP uncertainty score for the given responses.

        Parameters:
            responses (List[str]): List of response texts to evaluate

        Returns:
            Dict[str, Any]: Dictionary containing uncertainty scores and detailed metrics
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 1.0,
                "error": "Need at least 2 responses for LofreeCP computation",
                "method": "LofreeCP"
            }

        try:
            # Convert responses to frequency dictionary
            freq_dict = self._responses_to_frequency_dict(responses)

            if not freq_dict:
                return {
                    "uncertainty_score": 1.0,
                    "error": "No valid responses after preprocessing",
                    "method": "LofreeCP"
                }

            # Compute LofreeCP scores for all responses
            lofreecp_scores = self._compute_lofreecp_scores(freq_dict)

            # Compute overall uncertainty metrics
            normalized_entropy = self._compute_normalized_entropy(freq_dict)

            # Get the most frequent response and its uncertainty
            most_frequent_response = next(iter(freq_dict.keys()))
            most_frequent_score = lofreecp_scores.get(most_frequent_response, 1.0)

            # Overall uncertainty score (normalized to 0-1 range)
            # Higher LofreeCP score means higher uncertainty
            max_possible_score = 10.0 + self.lambda1  # Theoretical maximum
            uncertainty_score = min(1.0, most_frequent_score / max_possible_score)

            # Additional metrics
            unique_responses = len(freq_dict)
            total_responses = len(responses)
            response_diversity = unique_responses / total_responses

            return {
                "uncertainty_score": uncertainty_score,
                "method": "LofreeCP",
                "num_responses": total_responses,
                "unique_responses": unique_responses,
                "response_diversity": response_diversity,
                "normalized_entropy": normalized_entropy,
                "most_frequent_response": most_frequent_response,
                "most_frequent_score": most_frequent_score,

                # Detailed LofreeCP metrics
                "frequency_distribution": freq_dict,
                "lofreecp_scores": lofreecp_scores,
                "lambda1": self.lambda1,
                "lambda2": self.lambda2,

                # Original responses
                "original_responses": responses,

                "metadata": {
                    "embedding_model": self.embedding_model,
                    "embedding_available": self.embedding_encoder is not None,
                    "lambda1": self.lambda1,
                    "lambda2": self.lambda2,
                    "verbose": self.verbose
                }
            }

        except Exception as e:
            log.error(f"Error computing LofreeCP uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "LofreeCP"
            }

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 10  # LofreeCP works better with more samples for frequency estimation

    def get_method_name(self) -> str:
        """Get the method name."""
        return "LofreeCP"

    def __str__(self):
        return f"LofreeCP(λ1={self.lambda1}, λ2={self.lambda2})"